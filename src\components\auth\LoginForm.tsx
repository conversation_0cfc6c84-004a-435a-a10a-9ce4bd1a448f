import React, { useState } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { Input, Button, Text } from '../ui';

export interface LoginFormProps {
  onSubmit?: (credentials: { email: string; password: string }) => void;
  onForgotPassword?: () => void;
  onSwitchToOTP?: () => void;
  loading?: boolean;
  error?: string;
  className?: string;
  'data-testid'?: string;
}

export function LoginForm({
  onSubmit,
  onForgotPassword,
  onSwitchToOTP,
  loading = false,
  error,
  className = '',
  'data-testid': testId,
}: LoginFormProps) {
  const { colors } = useThemeStore();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm() && onSubmit) {
      onSubmit(formData);
    }
  };

  const handleInputChange =
    (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData(prev => ({ ...prev, [field]: e.target.value }));
      // Clear field error when user starts typing
      if (fieldErrors[field]) {
        setFieldErrors(prev => ({ ...prev, [field]: '' }));
      }
    };

  const EmailIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
      />
    </svg>
  );

  const PasswordIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
      />
    </svg>
  );

  const EyeIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
      />
    </svg>
  );

  const EyeOffIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
      />
    </svg>
  );

  return (
    <form
      onSubmit={handleSubmit}
      className={`space-y-6 ${className}`}
      data-testid={testId}
    >
      {/* Global Error */}
      {error && (
        <div
          className="p-4 rounded-lg border"
          style={{
            backgroundColor: `${colors.error}10`,
            borderColor: colors.error,
          }}
        >
          <Text variant="caption" color="error">
            {error}
          </Text>
        </div>
      )}

      {/* Email Field */}
      <Input
        type="email"
        label="Email Address"
        placeholder="Enter your email"
        value={formData.email}
        onChange={handleInputChange('email')}
        error={fieldErrors.email}
        startIcon={<EmailIcon />}
        fullWidth
        disabled={loading}
        data-testid="login-email"
      />

      {/* Password Field */}
      <div className="relative">
        <Input
          type={showPassword ? 'text' : 'password'}
          label="Password"
          placeholder="Enter your password"
          value={formData.password}
          onChange={handleInputChange('password')}
          error={fieldErrors.password}
          startIcon={<PasswordIcon />}
          fullWidth
          disabled={loading}
          data-testid="login-password"
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute right-3 top-9 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
          disabled={loading}
          aria-label={showPassword ? 'Hide password' : 'Show password'}
        >
          {showPassword ? <EyeOffIcon /> : <EyeIcon />}
        </button>
      </div>

      {/* Remember Me & Forgot Password */}
      <div className="flex items-center justify-between">
        <label className="flex items-center">
          <input
            type="checkbox"
            className="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
            disabled={loading}
          />
          <Text variant="caption" color="secondary" className="ml-2">
            Remember me
          </Text>
        </label>

        <Button
          variant="link"
          size="sm"
          onClick={onForgotPassword}
          disabled={loading}
        >
          <Text variant="caption" color="primary">
            Forgot password?
          </Text>
        </Button>
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        variant="primary"
        size="lg"
        fullWidth
        loading={loading}
        disabled={loading}
        data-testid="login-submit"
      >
        Sign In
      </Button>

      {/* Alternative Login */}
      <div className="text-center space-y-3">
        <Text variant="caption" color="secondary" align="center">
          Or sign in with
        </Text>
        <Button
          variant="link"
          size="sm"
          onClick={onSwitchToOTP}
          disabled={loading}
          fullWidth
        >
          <Text variant="caption" color="primary">
            OTP (WhatsApp/SMS)
          </Text>
        </Button>
      </div>
    </form>
  );
}
