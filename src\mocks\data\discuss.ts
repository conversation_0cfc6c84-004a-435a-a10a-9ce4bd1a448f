// Mock data for Discuss module
import type {
  User,
  Message,
  Channel,
  DirectMessage,
  Team,
  NotificationSettings,
  PresenceInfo,
  Call,
  CallParticipant,
} from '../../modules/discuss/types';

// Mock users
export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'JD',
    status: 'online',
    lastSeen: new Date(),
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'JS',
    status: 'away',
    lastSeen: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'MW',
    status: 'online',
    lastSeen: new Date(),
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'SJ',
    status: 'offline',
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
  },
  {
    id: '5',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'DB',
    status: 'busy',
    lastSeen: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
  },
];

// Mock channels
export const mockChannels: Channel[] = [
  {
    id: 'general',
    name: 'general',
    description: 'General discussion and announcements for the team',
    type: 'public',
    memberIds: ['1', '2', '3', '4', '5'],
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    lastActivity: new Date(),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'development',
    name: 'development',
    description: 'Development discussions and code reviews',
    type: 'public',
    memberIds: ['1', '3', '5'],
    createdBy: '1',
    createdAt: new Date('2024-01-02'),
    lastActivity: new Date(Date.now() - 30 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'design',
    name: 'design',
    description: 'Design discussions and feedback',
    type: 'public',
    memberIds: ['2', '4'],
    createdBy: '2',
    createdAt: new Date('2024-01-03'),
    lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'marketing',
    name: 'marketing',
    description: 'Marketing campaigns and strategies',
    type: 'private',
    memberIds: ['2', '4', '5'],
    createdBy: '2',
    createdAt: new Date('2024-01-04'),
    lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: false,
    },
  },
  {
    id: 'random',
    name: 'random',
    description: 'Random conversations and fun stuff',
    type: 'public',
    memberIds: ['1', '2', '3', '4', '5'],
    createdBy: '3',
    createdAt: new Date('2024-01-05'),
    lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: false,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
];

// Mock messages with comprehensive data
export const mockMessages: Message[] = [
  // General channel messages
  {
    id: 'msg-1',
    content: 'Welcome everyone to our new discuss platform! 🎉 Feel free to share ideas, ask questions, and collaborate here.',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date('2024-01-10T09:30:00'),
    reactions: [
      { emoji: '👍', userIds: ['2', '3', '4'], count: 3 },
      { emoji: '🎉', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-2',
    content: 'This looks great! I love the clean interface. Can we also add file sharing capabilities?',
    authorId: '2',
    channelId: 'general',
    timestamp: new Date('2024-01-10T10:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['1'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-3',
    content: '@Jane Smith Yes! File sharing is definitely on the roadmap. We\'re also planning to add video calls and screen sharing.',
    authorId: '3',
    channelId: 'general',
    timestamp: new Date('2024-01-10T11:00:00'),
    reactions: [],
    attachments: [],
    mentions: ['2'],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-4',
    content: 'I\'ve uploaded the latest design mockups for review. Please take a look and let me know your thoughts!',
    authorId: '4',
    channelId: 'general',
    timestamp: new Date('2024-01-10T14:30:00'),
    reactions: [
      { emoji: '👀', userIds: ['1', '2', '3'], count: 3 },
      { emoji: '🔥', userIds: ['1'], count: 1 },
    ],
    attachments: [
      {
        id: 'att-1',
        name: 'design-mockups-v2.pdf',
        type: 'document',
        url: '/mock-files/design-mockups-v2.pdf',
        size: 2048576, // 2MB
        mimeType: 'application/pdf',
      },
      {
        id: 'att-2',
        name: 'homepage-screenshot.png',
        type: 'image',
        url: '/mock-files/homepage-screenshot.png',
        size: 512000, // 500KB
        mimeType: 'image/png',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-5',
    content: 'Great work @Sarah Johnson! The new color scheme looks much more professional. 💯',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date('2024-01-10T15:45:00'),
    reactions: [
      { emoji: '💯', userIds: ['2', '3', '4'], count: 3 },
    ],
    attachments: [],
    mentions: ['4'],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // Development channel messages
  {
    id: 'msg-6',
    content: 'Just pushed the new authentication system to the dev branch. Ready for testing!',
    authorId: '3',
    channelId: 'development',
    timestamp: new Date('2024-01-10T09:00:00'),
    reactions: [
      { emoji: '🚀', userIds: ['1', '5'], count: 2 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-7',
    content: 'Found a bug in the user registration flow. Creating a ticket now.',
    authorId: '5',
    channelId: 'development',
    timestamp: new Date('2024-01-10T11:30:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-8',
    content: 'Here\'s the error log from the staging server. Looks like a database connection issue.',
    authorId: '5',
    channelId: 'development',
    timestamp: new Date('2024-01-10T12:00:00'),
    reactions: [],
    attachments: [
      {
        id: 'att-3',
        name: 'error-log-2024-01-10.txt',
        type: 'document',
        url: '/mock-files/error-log-2024-01-10.txt',
        size: 15360, // 15KB
        mimeType: 'text/plain',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // Design channel messages
  {
    id: 'msg-9',
    content: 'Working on the new icon set for the mobile app. Here\'s the progress so far:',
    authorId: '2',
    channelId: 'design',
    timestamp: new Date('2024-01-09T16:20:00'),
    reactions: [
      { emoji: '🎨', userIds: ['4'], count: 1 },
    ],
    attachments: [
      {
        id: 'att-4',
        name: 'mobile-icons-preview.png',
        type: 'image',
        url: '/mock-files/mobile-icons-preview.png',
        size: 768000, // 750KB
        mimeType: 'image/png',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-10',
    content: 'Love the minimalist approach! Can we make the notification icon a bit more prominent?',
    authorId: '4',
    channelId: 'design',
    timestamp: new Date('2024-01-09T17:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // Direct messages (no channelId)
  {
    id: 'msg-11',
    content: 'Hey! I wanted to discuss the new project requirements with you. Do you have some time today?',
    authorId: '1',
    timestamp: new Date('2024-01-09T15:45:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-12',
    content: 'Sure! I\'m free after 5 PM. Should we schedule a quick call?',
    authorId: '2',
    timestamp: new Date('2024-01-09T16:12:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-13',
    content: 'Perfect! Let\'s do a video call at 5:30 PM. I\'ll send you the meeting link.',
    authorId: '1',
    timestamp: new Date('2024-01-09T16:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-14',
    content: 'Good morning! I\'ve reviewed the requirements document. Looks great overall, just have a few questions about the timeline.',
    authorId: '1',
    timestamp: new Date('2024-01-10T09:00:00'),
    reactions: [],
    attachments: [
      {
        id: 'att-5',
        name: 'project-requirements-v3.docx',
        type: 'document',
        url: '/mock-files/project-requirements-v3.docx',
        size: 1024000, // 1MB
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-15',
    content: 'Thanks for the review! I\'ll address those timeline questions in our call today.',
    authorId: '2',
    timestamp: new Date('2024-01-10T09:30:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // More recent messages for testing
  {
    id: 'msg-16',
    content: 'Just deployed the hotfix to production. Everything looks good! 🎉',
    authorId: '5',
    channelId: 'development',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    reactions: [
      { emoji: '🎉', userIds: ['1', '3'], count: 2 },
      { emoji: '🚀', userIds: ['1'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-17',
    content: 'Team meeting in 30 minutes! Don\'t forget to join the video call.',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    reactions: [
      { emoji: '📅', userIds: ['2', '3', '4', '5'], count: 4 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-18',
    content: 'Can someone help me with the CSS animation issue? It\'s not working in Safari.',
    authorId: '3',
    channelId: 'development',
    timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'delivered',
  },
  {
    id: 'msg-19',
    content: 'I can help! Safari has some quirks with CSS animations. Let me take a look.',
    authorId: '1',
    channelId: 'development',
    timestamp: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
    reactions: [
      { emoji: '🙏', userIds: ['3'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'delivered',
  },
  {
    id: 'msg-20',
    content: 'Quick question about the API endpoint for user preferences. Is it /api/users/:id/preferences?',
    authorId: '2',
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'sent',
  },
];

// Mock direct messages
export const mockDirectMessages: DirectMessage[] = [
  {
    id: 'dm-1-2',
    participantIds: ['1', '2'],
    lastMessage: mockMessages.find(m => m.id === 'msg-5'),
    lastActivity: new Date('2024-01-09T16:12:00'),
    isArchived: false,
  },
  {
    id: 'dm-1-3',
    participantIds: ['1', '3'],
    lastActivity: new Date('2024-01-08T14:30:00'),
    isArchived: false,
  },
];

// Mock teams
export const mockTeams: Team[] = [
  {
    id: 'frontend-team',
    name: 'Frontend Team',
    description: 'UI/UX development and design implementation',
    memberIds: ['1', '2', '4'],
    channelIds: ['general', 'design'],
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    settings: {
      visibility: 'public',
      joinPolicy: 'open',
      allowMemberInvites: true,
    },
  },
  {
    id: 'backend-team',
    name: 'Backend Team',
    description: 'Server-side development and API management',
    memberIds: ['3', '5'],
    channelIds: ['development'],
    createdBy: '3',
    createdAt: new Date('2024-01-02'),
    settings: {
      visibility: 'public',
      joinPolicy: 'invite-only',
      allowMemberInvites: false,
    },
  },
];

// Mock notification settings
export const mockNotificationSettings: NotificationSettings = {
  desktop: true,
  sound: true,
  email: false,
  mobile: true,
  mentions: true,
  directMessages: true,
  channels: true,
  doNotDisturbStart: '22:00',
  doNotDisturbEnd: '08:00',
};

// Mock presence info
export const mockPresenceInfo: PresenceInfo[] = [
  {
    userId: '1',
    status: 'online',
    lastSeen: new Date(),
    isTyping: false,
  },
  {
    userId: '2',
    status: 'away',
    lastSeen: new Date(Date.now() - 15 * 60 * 1000),
    isTyping: true,
    currentChannel: 'general',
  },
  {
    userId: '3',
    status: 'online',
    lastSeen: new Date(),
    isTyping: false,
  },
  {
    userId: '4',
    status: 'offline',
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000),
    isTyping: false,
  },
  {
    userId: '5',
    status: 'busy',
    lastSeen: new Date(Date.now() - 30 * 60 * 1000),
    isTyping: false,
  },
];

// Helper functions to get mock data
export const getMockUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const getMockChannelById = (id: string): Channel | undefined => {
  return mockChannels.find(channel => channel.id === id);
};

export const getMockMessagesByChannelId = (channelId: string): Message[] => {
  return mockMessages.filter(message => message.channelId === channelId);
};

export const getMockDirectMessagesByUserId = (userId: string): DirectMessage[] => {
  return mockDirectMessages.filter(dm => dm.participantIds.includes(userId));
};

export const getMockTeamById = (id: string): Team | undefined => {
  return mockTeams.find(team => team.id === id);
};

// Mock calls
export const mockCalls: Call[] = [
  {
    id: 'call-1',
    type: 'video',
    channelId: 'general',
    participantIds: ['1', '2', '3'],
    startedBy: '1',
    startedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    endedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 15 * 60 * 1000), // 15 minutes duration
    status: 'ended',
    recordingUrl: '/recordings/call-1.mp4',
  },
  {
    id: 'call-2',
    type: 'voice',
    channelId: 'development',
    participantIds: ['2', '3', '4'],
    startedBy: '2',
    startedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    endedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 8 * 60 * 1000), // 8 minutes duration
    status: 'ended',
  },
  {
    id: 'call-3',
    type: 'video',
    participantIds: ['1', '4'], // Direct call
    startedBy: '4',
    startedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    endedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 25 * 60 * 1000), // 25 minutes duration
    status: 'ended',
    recordingUrl: '/recordings/call-3.mp4',
  },
  {
    id: 'call-4',
    type: 'voice',
    channelId: 'general',
    participantIds: ['1', '2', '3', '5'],
    startedBy: '3',
    startedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    endedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 45 * 60 * 1000), // 45 minutes duration
    status: 'ended',
  },
  {
    id: 'call-5',
    type: 'video',
    channelId: 'design',
    participantIds: ['2', '5'],
    startedBy: '5',
    startedAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago (ongoing)
    status: 'active',
  },
];

// Mock call participants
export const mockCallParticipants: { [callId: string]: CallParticipant[] } = {
  'call-1': [
    {
      userId: '1',
      joinedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      leftAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 15 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: false,
    },
    {
      userId: '2',
      joinedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30 * 1000),
      leftAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 15 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: true,
    },
    {
      userId: '3',
      joinedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 2 * 60 * 1000),
      leftAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 12 * 60 * 1000),
      isMuted: true,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
  ],
  'call-2': [
    {
      userId: '2',
      joinedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      leftAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 8 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
    {
      userId: '3',
      joinedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 15 * 1000),
      leftAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 8 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
    {
      userId: '4',
      joinedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 45 * 1000),
      leftAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 6 * 60 * 1000),
      isMuted: true,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
  ],
  'call-5': [
    {
      userId: '2',
      joinedAt: new Date(Date.now() - 5 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: false,
    },
    {
      userId: '5',
      joinedAt: new Date(Date.now() - 5 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: true,
    },
  ],
};

// Helper functions for calls
export const getMockCallById = (id: string): Call | undefined => {
  return mockCalls.find(call => call.id === id);
};

export const getMockCallsByChannelId = (channelId: string): Call[] => {
  return mockCalls.filter(call => call.channelId === channelId);
};

export const getMockCallsByUserId = (userId: string): Call[] => {
  return mockCalls.filter(call => call.participantIds.includes(userId));
};

export const getMockCallParticipants = (callId: string): CallParticipant[] => {
  return mockCallParticipants[callId] || [];
};
