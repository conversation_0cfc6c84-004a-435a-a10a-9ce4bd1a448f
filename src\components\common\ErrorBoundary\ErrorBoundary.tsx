/**
 * ErrorBoundary Component
 * React error boundary with enhanced error handling, reporting, and recovery features
 */

import React, { Component } from 'react';
import type { ReactNode, ErrorInfo } from 'react';
import type { AppError } from '../../../utils/errorTypes';
import { createAppError } from '../../../utils/errorTypes';
import { reportError } from '../../../utils/errorReporting';
import { executeAutoRecovery } from '../../../utils/errorRecovery';
import { useThemeStore } from '../../../stores/themeStore';
import ErrorFallback from '../ErrorFallback/ErrorFallback';

// Theme-aware recovery loading component
const RecoveryLoading: React.FC = () => {
  const { colors } = useThemeStore();

  return (
    <div className="flex items-center justify-center p-8">
      <div className="text-center">
        <div
          className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4"
          style={{ borderColor: colors.primary }}
        ></div>
        <p style={{ color: colors.textSecondary }}>Attempting to recover...</p>
      </div>
    </div>
  );
};

// Props interface for ErrorBoundary
export interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: AppError, errorInfo: ErrorInfo) => void;
  enableAutoRecovery?: boolean;
  enableReporting?: boolean;
  isolateErrors?: boolean;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
  level?: 'page' | 'section' | 'component';
  componentName?: string;
  'data-testid'?: string;
}

// Props for fallback component
export interface ErrorFallbackProps {
  error: AppError;
  resetError: () => void;
  componentName?: string;
  level?: 'page' | 'section' | 'component';
}

// State interface for ErrorBoundary
interface ErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
  errorId: string | null;
  retryCount: number;
  isRecovering: boolean;
}

/**
 * Enhanced React Error Boundary with comprehensive error handling
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;
  private autoRecoveryTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorId: null,
      retryCount: 0,
      isRecovering: false,
    };
  }

  /**
   * Static method to derive state from error
   */
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Create enhanced error object
    const appError = createAppError(error, undefined, {
      component: 'ErrorBoundary',
      timestamp: new Date(),
    });

    return {
      hasError: true,
      error: appError,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    };
  }

  /**
   * Component did catch - handles error reporting and recovery
   */
  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    const {
      onError,
      enableReporting = true,
      enableAutoRecovery = true,
      componentName,
    } = this.props;

    // Get the enhanced error from state (already set by getDerivedStateFromError)
    const appError =
      this.state.error ||
      createAppError(error, undefined, {
        component: componentName || 'Unknown',
        route: window.location.pathname,
        additionalData: {
          componentStack: errorInfo.componentStack,
          errorBoundary: true,
          level: this.props.level || 'component',
        },
      });

    // Call custom error handler
    if (onError) {
      try {
        onError(appError, errorInfo);
      } catch (handlerError) {
        console.error('Error in custom error handler:', handlerError);
      }
    }

    // Report error
    if (enableReporting) {
      this.reportError(appError, errorInfo);
    }

    // Attempt auto recovery (but only if not already recovering)
    if (enableAutoRecovery && !this.state.isRecovering) {
      this.attemptAutoRecovery(appError);
    }
  }

  /**
   * Component did update - handle reset on props change
   */
  componentDidUpdate(prevProps: ErrorBoundaryProps): void {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    if (hasError && resetOnPropsChange) {
      // Check if reset keys have changed
      if (resetKeys && prevProps.resetKeys) {
        const hasResetKeyChanged = resetKeys.some(
          (key, index) => key !== prevProps.resetKeys![index]
        );

        if (hasResetKeyChanged) {
          this.resetError();
        }
      }
    }
  }

  /**
   * Component will unmount - cleanup
   */
  componentWillUnmount(): void {
    this.clearTimeouts();
  }

  /**
   * Reports error with comprehensive context
   */
  private async reportError(
    error: AppError,
    errorInfo: ErrorInfo
  ): Promise<void> {
    try {
      await reportError(error, {
        componentStack: errorInfo.componentStack,
        errorBoundaryLevel: this.props.level,
        componentName: this.props.componentName,
        retryCount: this.state.retryCount,
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  /**
   * Attempts automatic error recovery
   */
  private async attemptAutoRecovery(error: AppError): Promise<void> {
    const { componentName } = this.props;
    const { retryCount } = this.state;

    // Prevent infinite recovery attempts
    if (retryCount >= 3) {
      console.warn('Max retry attempts reached, stopping auto recovery');
      return;
    }

    this.setState({ isRecovering: true });

    try {
      const recoveryContext = {
        error,
        component: componentName,
        route: window.location.pathname,
        retryCount,
      };

      const recovered = await executeAutoRecovery(recoveryContext);

      if (recovered) {
        // Auto recovery successful - reset after a short delay to prevent immediate re-error
        this.autoRecoveryTimeoutId = window.setTimeout(() => {
          this.resetError();
        }, 100);
        return;
      }

      // Auto recovery failed, but we can still try manual recovery
      this.setState({ isRecovering: false });
    } catch (recoveryError) {
      console.error('Auto recovery failed:', recoveryError);
      this.setState({ isRecovering: false });
    }
  }

  /**
   * Resets the error boundary state
   */
  private resetError = (): void => {
    this.clearTimeouts();

    this.setState({
      hasError: false,
      error: null,
      errorId: null,
      retryCount: this.state.retryCount + 1,
      isRecovering: false,
    });
  };

  /**
   * Clears all timeouts
   */
  private clearTimeouts(): void {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }
    if (this.autoRecoveryTimeoutId) {
      clearTimeout(this.autoRecoveryTimeoutId);
      this.autoRecoveryTimeoutId = null;
    }
  }

  /**
   * Renders the component
   */
  render(): ReactNode {
    const { hasError, error, isRecovering } = this.state;
    const {
      children,
      fallback: FallbackComponent = ErrorFallback,
      isolateErrors,
      level,
      componentName,
    } = this.props;

    if (hasError && error) {
      // Show recovery indicator
      if (isRecovering) {
        return <RecoveryLoading />;
      }

      // Render fallback UI
      return (
        <FallbackComponent
          error={error}
          resetError={this.resetError}
          componentName={componentName}
          level={level}
        />
      );
    }

    // Isolate errors to prevent propagation
    if (isolateErrors) {
      try {
        return children;
      } catch (renderError) {
        // Catch synchronous render errors
        const appError = createAppError(renderError, undefined, {
          component: componentName || 'Unknown',
          route: window.location.pathname,
        });

        return (
          <FallbackComponent
            error={appError}
            resetError={this.resetError}
            componentName={componentName}
            level={level}
          />
        );
      }
    }

    return children;
  }
}

export default ErrorBoundary;
