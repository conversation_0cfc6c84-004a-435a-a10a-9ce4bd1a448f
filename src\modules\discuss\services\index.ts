// Discuss module services
// These handle API calls and business logic for the discuss module

// Message services
export { messageService } from './messageService';
export type { SendMessageRequest, UpdateMessageRequest } from './messageService';

export { channelService } from './channelService';
export type { CreateChannelRequest, UpdateChannelRequest } from './channelService';

// Real-time services
export { websocketService } from './websocketService';
export type { WebSocketEventType, WebSocketEventHandler } from './websocketService';

// File services
export { fileService } from './fileService';
export type { UploadFileRequest, UploadProgress, FileUploadResponse } from './fileService';

// Search services
export { searchService } from './searchService';
export type { SearchFilters, SearchResult, SearchResponse } from './searchService';

// Presence services
export { presenceService } from './presenceService';
export type { UpdatePresenceRequest, TypingRequest } from './presenceService';

// User services
// export { userService } from './userService';

// Notification services
// export { notificationService } from './notificationService';

// File services
// export { fileUploadService } from './fileUploadService';
// export { fileDownloadService } from './fileDownloadService';

// TODO: Implement remaining services
