import React, { useState, useEffect } from 'react';
import { callService } from '../../services/callService';
import { getMockUserById } from '../../../../mocks/data/discuss';
import type { Call } from '../../types';

interface CallHistoryProps {
  channelId?: string;
  userId?: string;
  className?: string;
}

export const CallHistory: React.FC<CallHistoryProps> = ({
  channelId,
  userId,
  className = '',
}) => {
  const [calls, setCalls] = useState<Call[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadCallHistory();
  }, [channelId, userId]);

  const loadCallHistory = async (pageNum: number = 1) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await callService.getCallHistory(channelId, userId, pageNum, 20);
      
      if (pageNum === 1) {
        setCalls(response.data);
      } else {
        setCalls(prev => [...prev, ...response.data]);
      }
      
      setHasMore(response.data.length === 20);
      setPage(pageNum);
    } catch (err) {
      setError('Failed to load call history');
      console.error('Error loading call history:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadMore = () => {
    if (!isLoading && hasMore) {
      loadCallHistory(page + 1);
    }
  };

  const formatDuration = (startedAt: Date, endedAt?: Date): string => {
    if (!endedAt) return 'Ongoing';
    
    const duration = Math.floor((new Date(endedAt).getTime() - new Date(startedAt).getTime()) / 1000);
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const seconds = duration % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatDate = (date: Date): string => {
    const now = new Date();
    const callDate = new Date(date);
    const diffInDays = Math.floor((now.getTime() - callDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return callDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInDays === 1) {
      return `Yesterday ${callDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffInDays < 7) {
      return callDate.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return callDate.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  const getCallIcon = (call: Call) => {
    const iconClass = "w-5 h-5";
    
    if (call.type === 'video') {
      return (
        <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
        </svg>
      );
    }
    
    return (
      <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
      </svg>
    );
  };

  const getStatusColor = (status: Call['status']) => {
    switch (status) {
      case 'active':
        return 'text-green-500';
      case 'ended':
        return 'text-gray-500 dark:text-gray-400';
      case 'ringing':
        return 'text-yellow-500';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  if (isLoading && calls.length === 0) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 text-center ${className}`}>
        <div className="text-red-500 dark:text-red-400 mb-2">
          <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </div>
        <button
          onClick={() => loadCallHistory()}
          className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
        >
          Try again
        </button>
      </div>
    );
  }

  if (calls.length === 0) {
    return (
      <div className={`p-8 text-center ${className}`}>
        <div className="text-gray-500 dark:text-gray-400">
          <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
          </svg>
          <p className="text-lg font-medium mb-2">No call history</p>
          <p className="text-sm">Start a voice or video call to see your history here.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <div className="space-y-1">
        {calls.map((call) => {
          const startedByUser = getMockUserById(call.startedBy);
          const participantCount = call.participantIds.length;
          
          return (
            <div
              key={call.id}
              className="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
            >
              <div className={`flex-shrink-0 mr-3 ${getStatusColor(call.status)}`}>
                {getCallIcon(call)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {call.type === 'video' ? 'Video call' : 'Voice call'}
                    {participantCount > 2 && ` (${participantCount} participants)`}
                  </p>
                  <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                    {formatDate(call.startedAt)}
                  </span>
                </div>
                
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    Started by {startedByUser?.name || 'Unknown'}
                  </p>
                  <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                    {formatDuration(call.startedAt, call.endedAt)}
                  </span>
                </div>
                
                {call.recordingUrl && (
                  <div className="mt-1">
                    <a
                      href={call.recordingUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 flex items-center"
                    >
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" />
                      </svg>
                      Recording available
                    </a>
                  </div>
                )}
              </div>
              
              <div className="flex-shrink-0 ml-2">
                <div className={`w-2 h-2 rounded-full ${
                  call.status === 'active' ? 'bg-green-500' :
                  call.status === 'ringing' ? 'bg-yellow-500' :
                  'bg-gray-400'
                }`}></div>
              </div>
            </div>
          );
        })}
      </div>
      
      {hasMore && (
        <div className="p-4 text-center">
          <button
            onClick={loadMore}
            disabled={isLoading}
            className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Load more'}
          </button>
        </div>
      )}
    </div>
  );
};

// Compact version for sidebars
export const CallHistoryCompact: React.FC<CallHistoryProps> = (props) => {
  return (
    <div className="space-y-1">
      <CallHistory {...props} className="max-h-64 overflow-y-auto" />
    </div>
  );
};
