// Discuss module type definitions

// Core types
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen?: Date;
}

export interface Message {
  id: string;
  content: string;
  authorId: string;
  channelId?: string;
  threadId?: string;
  parentMessageId?: string;
  timestamp: Date;
  editedAt?: Date;
  reactions: Reaction[];
  attachments: Attachment[];
  mentions: string[];
  isDeleted: boolean;
  deliveryStatus: 'sent' | 'delivered' | 'read' | 'failed';
}

export interface Channel {
  id: string;
  name: string;
  description?: string;
  type: 'public' | 'private' | 'direct';
  memberIds: string[];
  createdBy: string;
  createdAt: Date;
  lastActivity?: Date;
  isArchived: boolean;
  settings: ChannelSettings;
}

export interface DirectMessage {
  id: string;
  participantIds: string[];
  lastMessage?: Message;
  lastActivity: Date;
  isArchived: boolean;
}

export interface Team {
  id: string;
  name: string;
  description?: string;
  memberIds: string[];
  channelIds: string[];
  createdBy: string;
  createdAt: Date;
  settings: TeamSettings;
}

export interface Reaction {
  emoji: string;
  userIds: string[];
  count: number;
}

export interface Attachment {
  id: string;
  name: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'other';
  url: string;
  size: number;
  mimeType: string;
}

export interface ChannelSettings {
  notifications: boolean;
  muteUntil?: Date;
  allowFileUploads: boolean;
  allowExternalLinks: boolean;
  retentionPolicy?: RetentionPolicy;
}

export interface TeamSettings {
  visibility: 'public' | 'private';
  joinPolicy: 'open' | 'invite-only' | 'admin-approval';
  allowMemberInvites: boolean;
}

export interface RetentionPolicy {
  enabled: boolean;
  duration: number; // in days
  autoDelete: boolean;
}

export interface NotificationSettings {
  desktop: boolean;
  sound: boolean;
  email: boolean;
  mobile: boolean;
  mentions: boolean;
  directMessages: boolean;
  channels: boolean;
  doNotDisturbStart?: string;
  doNotDisturbEnd?: string;
}

export interface PresenceInfo {
  userId: string;
  status: User['status'];
  lastSeen: Date;
  isTyping: boolean;
  currentChannel?: string;
}

// Event types for real-time updates
export interface MessageEvent {
  type: 'message_created' | 'message_updated' | 'message_deleted';
  message: Message;
  channelId: string;
}

export interface PresenceEvent {
  type: 'user_online' | 'user_offline' | 'user_typing' | 'user_stopped_typing';
  userId: string;
  channelId?: string;
  presence: PresenceInfo;
}

export interface ChannelEvent {
  type: 'channel_created' | 'channel_updated' | 'channel_deleted' | 'user_joined' | 'user_left';
  channel: Channel;
  userId?: string;
}

// API response types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Search types
export interface SearchQuery {
  query: string;
  channelId?: string;
  userId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  messageType?: 'text' | 'file' | 'image' | 'video';
}

export interface SearchResult {
  message: Message;
  channel: Channel;
  author: User;
  highlights: string[];
}

// Integration types
export interface Integration {
  id: string;
  name: string;
  type: 'webhook' | 'bot' | 'external_app';
  enabled: boolean;
  config: Record<string, any>;
}

export interface Webhook {
  id: string;
  url: string;
  events: string[];
  secret?: string;
  enabled: boolean;
}

// Bot types
export interface Bot {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  commands: BotCommand[];
  enabled: boolean;
}

export interface BotCommand {
  command: string;
  description: string;
  usage: string;
  permissions: string[];
}

// File upload types
export interface FileUploadProgress {
  fileId: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'failed';
  error?: string;
}

// Voice/Video call types
export interface Call {
  id: string;
  type: 'voice' | 'video';
  channelId?: string;
  participantIds: string[];
  startedBy: string;
  startedAt: Date;
  endedAt?: Date;
  status: 'ringing' | 'active' | 'ended';
  recordingUrl?: string;
}

export interface CallParticipant {
  userId: string;
  joinedAt: Date;
  leftAt?: Date;
  isMuted: boolean;
  isVideoEnabled: boolean;
  isScreenSharing: boolean;
}

// Export all types
export type * from './index';
