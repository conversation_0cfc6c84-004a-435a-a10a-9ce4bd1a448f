import React, { forwardRef } from 'react';
import { useThemeStore } from '../../../stores/themeStore';

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  fullWidth?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  'data-testid'?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      size = 'md',
      variant = 'default',
      fullWidth = false,
      startIcon,
      endIcon,
      className = '',
      disabled = false,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const { colors } = useThemeStore();

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-4 py-3 text-lg',
    };

    const baseInputClasses =
      'w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed';

    const variantStyles = {
      default: {
        backgroundColor: colors.background,
        borderColor: error ? colors.error : colors.border,
        color: colors.text,
        '--focus-ring': error ? colors.error : colors.primary,
        '--focus-border': error ? colors.error : colors.primary,
      },
      filled: {
        backgroundColor: colors.surface,
        borderColor: 'transparent',
        color: colors.text,
        '--focus-ring': error ? colors.error : colors.primary,
        '--focus-border': error ? colors.error : colors.primary,
      },
      outlined: {
        backgroundColor: colors.background,
        borderColor: error ? colors.error : colors.borderSecondary,
        color: colors.text,
        '--focus-ring': error ? colors.error : colors.primary,
        '--focus-border': error ? colors.error : colors.primary,
      },
    };

    const inputClasses = `
    ${baseInputClasses}
    ${sizeClasses[size]}
    ${startIcon ? 'pl-10' : ''}
    ${endIcon ? 'pr-10' : ''}
    ${fullWidth ? 'w-full' : ''}
    ${className}
  `.trim();

    const labelClasses = `block text-sm font-medium mb-1`;
    const helperTextClasses = `mt-1 text-sm`;

    return (
      <div className={fullWidth ? 'w-full' : ''}>
        {label && (
          <label
            className={labelClasses}
            style={{ color: error ? colors.error : colors.text }}
          >
            {label}
          </label>
        )}

        <div className="relative">
          {startIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div style={{ color: colors.mutedForeground }}>{startIcon}</div>
            </div>
          )}

          <input
            ref={ref}
            className={inputClasses}
            style={variantStyles[variant]}
            disabled={disabled}
            data-testid={testId}
            {...props}
          />

          {endIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <div style={{ color: colors.mutedForeground }}>{endIcon}</div>
            </div>
          )}
        </div>

        {(error || helperText) && (
          <p
            className={helperTextClasses}
            style={{ color: error ? colors.error : colors.mutedForeground }}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
