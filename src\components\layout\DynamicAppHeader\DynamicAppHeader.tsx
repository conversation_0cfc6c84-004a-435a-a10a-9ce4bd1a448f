import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../../stores/themeStore';
import {
  Button,
  Input,
  ThemeToggle,
  CompanySelector,
  UserAvatarDropdown,
} from '../../ui';
import CenteredSearchChipInput from '../../ui/CenteredSearchChipInput';
import type {
  FilterTag,
  FilterItem,
  GroupByItem,
  FavoriteItem,
} from '../../ui/CenteredSearchChipInput/CenteredSearchChipInput';
import { cn } from '../../../utils/cn';
import {
  MenuIcon,
  SearchIcon,
  BellIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  UploadIcon,
  XIcon,
} from '../../icons';

// Type Definitions
export interface DynamicAppHeaderProps {
  app: {
    name: string;
    icon: React.ReactNode;
    navLinks: { label: string; href: string; isActive?: boolean }[];
  };
  user: {
    name: string;
    avatar: React.ReactNode;
    notifications: { count: number; icon: React.ReactNode }[];
  };
  view: {
    title: string;
    actions: { label: string; onClick: () => void; isPrimary?: boolean }[];
    search: {
      filterTags?: FilterTag[];
      filterItems?: FilterItem[];
      groupByItems?: GroupByItem[];
      favoriteItems?: FavoriteItem[];
      onSearch?: (query: string) => void;
      onTagRemove?: (tagId: string) => void;
      onFilterSelect?: (filterId: string) => void;
      onGroupBySelect?: (groupId: string) => void;
      onFavoriteSelect?: (favoriteId: string) => void;
      onFavoriteDelete?: (favoriteId: string) => void;
      onAddCustomFilter?: () => void;
      onAddCustomGroup?: () => void;
      onSaveCurrentSearch?: () => void;
      // Legacy support for existing basic search
      filters?: { id: any; label: string }[];
      onRemoveFilter?: (id: any) => void;
    };
    pagination: {
      currentRange: string;
      onNext: () => void;
      onPrev: () => void;
    };
    viewModes: { name: string; icon: React.ReactNode }[];
    activeViewMode: string;
  };
  className?: string;
  'data-testid'?: string;
}

const DynamicAppHeader: React.FC<DynamicAppHeaderProps> = ({
  app,
  user,
  view,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isBackButtonHovered, setIsBackButtonHovered] = useState(false);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    view.search.onSearch?.(searchQuery);
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <header
      className={cn('border-b backdrop-blur-sm', className)}
      style={{
        backgroundColor: colors.surface,
        borderColor: colors.border,
      }}
      data-testid={testId}
    >
      {/* Top Bar - Global Navigation */}
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left: App Icon, Name, and Navigation */}
          <div className="flex items-center space-x-8">
            {/* App Icon and Name - Enhanced hover with back arrow */}
            <button
              className="flex items-center space-x-3 px-1 py-1 rounded-lg transition-all duration-200"
              style={{
                backgroundColor: 'transparent',
              }}
              onMouseEnter={e => {
                setIsBackButtonHovered(true);
                e.currentTarget.style.backgroundColor = `${colors.hover}15`;
              }}
              onMouseLeave={e => {
                setIsBackButtonHovered(false);
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
              onClick={handleBackToDashboard}
              aria-label="Back to dashboard"
              title="Back to app grid"
            >
              <div className="w-8 h-8 flex items-center justify-center relative overflow-hidden">
                {/* App Icon */}
                <div
                  className={`absolute inset-0 flex items-center justify-center transition-all duration-200 ${
                    isBackButtonHovered
                      ? 'transform translate-x-8 opacity-0'
                      : 'transform translate-x-0 opacity-100'
                  }`}
                >
                  {app.icon}
                </div>
                {/* Back Arrow */}
                <div
                  className={`absolute inset-0 flex items-center justify-center transition-all duration-200 ${
                    isBackButtonHovered
                      ? 'transform translate-x-0 opacity-100'
                      : 'transform -translate-x-8 opacity-0'
                  }`}
                  style={{ color: colors.primary }}
                >
                  <ChevronLeftIcon className="w-5 h-5" />
                </div>
              </div>
              <h1
                className={`text-lg font-semibold tracking-tight transition-all duration-200 ${
                  isBackButtonHovered
                    ? 'transform -translate-x-1'
                    : 'transform translate-x-0'
                }`}
                style={{
                  color: isBackButtonHovered ? colors.primary : colors.text,
                }}
              >
                {app.name}
              </h1>
            </button>

            {/* Desktop Navigation Links - Clean hover only */}
            <nav className="hidden lg:flex items-center space-x-1">
              {app.navLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="px-4 py-2 text-sm font-medium rounded-md transition-all duration-150"
                  style={{
                    backgroundColor: link.isActive
                      ? `${colors.primary}10`
                      : 'transparent',
                    color: link.isActive
                      ? colors.primary
                      : colors.mutedForeground,
                  }}
                  onMouseEnter={e => {
                    if (!link.isActive) {
                      e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                      e.currentTarget.style.color = colors.text;
                    }
                  }}
                  onMouseLeave={e => {
                    if (!link.isActive) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = colors.mutedForeground;
                    }
                  }}
                >
                  {link.label}
                </a>
              ))}
            </nav>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden p-2 rounded-md transition-colors duration-150"
              style={{
                backgroundColor: 'transparent',
                color: colors.mutedForeground,
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                e.currentTarget.style.color = colors.text;
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = colors.mutedForeground;
              }}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              <MenuIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Right: System & User Tray - Simplified */}
          <div className="flex items-center space-x-2">
            {/* Theme Toggle */}
            <ThemeToggle size="md" />

            {/* Company Selector */}
            <CompanySelector showLabel className="hidden sm:flex" />

            {/* Notifications - Clean hover only */}
            {user.notifications.map((notification, index) => (
              <button
                key={index}
                className="relative p-2 rounded-md transition-colors duration-150"
                style={{
                  backgroundColor: 'transparent',
                  color: colors.mutedForeground,
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                  e.currentTarget.style.color = colors.text;
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = colors.mutedForeground;
                }}
                aria-label={`Notifications (${notification.count})`}
              >
                {notification.icon}
                {notification.count > 0 && (
                  <span
                    className="absolute -top-0.5 -right-0.5 min-w-[18px] h-[18px] text-xs font-medium rounded-full flex items-center justify-center px-1"
                    style={{
                      backgroundColor: colors.error,
                      color: colors.errorForeground,
                    }}
                  >
                    {notification.count > 99 ? '99+' : notification.count}
                  </span>
                )}
              </button>
            ))}

            {/* User Avatar Dropdown */}
            <UserAvatarDropdown
              user={{
                name: user.name,
                avatar: user.avatar,
              }}
              onPreferences={() => console.log('Open preferences')}
              onLogout={() => console.log('Logout')}
              onDocumentation={() => console.log('Open documentation')}
              onShortcuts={() => console.log('Show shortcuts')}
              onInstallApp={() => console.log('Install app')}
              onOnboarding={() => console.log('Start onboarding')}
            />
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu - Simplified */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden border-t"
          style={{ borderColor: colors.border }}
        >
          <div className="px-4 py-4 space-y-1">
            {app.navLinks.map((link, index) => (
              <a
                key={index}
                href={link.href}
                className="block px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-150"
                style={{
                  backgroundColor: link.isActive
                    ? `${colors.primary}10`
                    : 'transparent',
                  color: link.isActive ? colors.primary : colors.text,
                }}
                onMouseEnter={e => {
                  if (!link.isActive) {
                    e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                  }
                }}
                onMouseLeave={e => {
                  if (!link.isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </a>
            ))}
          </div>
        </div>
      )}

      {/* Bottom Bar - Contextual Controls - Simplified */}
      <div className="border-t" style={{ borderColor: colors.border }}>
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-14">
            {/* Left: View Title and Actions */}
            <div className="flex items-center space-x-6">
              {/* View Title */}
              <h2
                className="text-lg font-semibold tracking-tight"
                style={{ color: colors.text }}
              >
                {view.title}
              </h2>

              {/* Action Buttons - Desktop */}
              <div className="hidden sm:flex items-center space-x-2">
                {view.actions.map((action, index) => (
                  <Button
                    key={index}
                    variant={action.isPrimary ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={action.onClick}
                    className="flex items-center space-x-2"
                  >
                    {action.label === 'New' && <PlusIcon className="w-4 h-4" />}
                    {action.label === 'Upload' && (
                      <UploadIcon className="w-4 h-4" />
                    )}
                    <span>{action.label}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Center: Search Bar - Simplified */}
            <div className="flex-1 max-w-xl mx-8 hidden lg:block">
              <CenteredSearchChipInput
                placeholder="Search..."
                filterTags={view.search.filterTags}
                filterItems={view.search.filterItems}
                groupByItems={view.search.groupByItems}
                favoriteItems={view.search.favoriteItems}
                onSearch={view.search.onSearch}
                onTagRemove={view.search.onTagRemove}
                onFilterSelect={view.search.onFilterSelect}
                onGroupBySelect={view.search.onGroupBySelect}
                onFavoriteSelect={view.search.onFavoriteSelect}
                onFavoriteDelete={view.search.onFavoriteDelete}
                onAddCustomFilter={view.search.onAddCustomFilter}
                onAddCustomGroup={view.search.onAddCustomGroup}
                onSaveCurrentSearch={view.search.onSaveCurrentSearch}
                className="w-full"
              />
            </div>

            {/* Right: Controls - Simplified */}
            <div className="flex items-center space-x-3">
              {/* Pagination - Clean hover only */}
              <div className="hidden md:flex items-center space-x-1">
                <button
                  onClick={view.pagination.onPrev}
                  className="p-2 rounded-md transition-colors duration-150"
                  style={{
                    backgroundColor: 'transparent',
                    color: colors.mutedForeground,
                  }}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                    e.currentTarget.style.color = colors.text;
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = colors.mutedForeground;
                  }}
                  aria-label="Previous page"
                >
                  <ChevronLeftIcon className="w-4 h-4" />
                </button>
                <span
                  className="text-sm font-medium px-3 py-1"
                  style={{ color: colors.textSecondary }}
                >
                  {view.pagination.currentRange}
                </span>
                <button
                  onClick={view.pagination.onNext}
                  className="p-2 rounded-md transition-colors duration-150"
                  style={{
                    backgroundColor: 'transparent',
                    color: colors.mutedForeground,
                  }}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                    e.currentTarget.style.color = colors.text;
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = colors.mutedForeground;
                  }}
                  aria-label="Next page"
                >
                  <ChevronRightIcon className="w-4 h-4" />
                </button>
              </div>

              {/* View Mode Switcher - Clean design */}
              <div
                className="hidden md:flex items-center space-x-1 p-1 rounded-lg"
                style={{
                  backgroundColor: 'transparent',
                }}
              >
                {view.viewModes.map(mode => (
                  <button
                    key={mode.name}
                    className="p-2 rounded-md transition-all duration-150"
                    style={{
                      backgroundColor:
                        mode.name === view.activeViewMode
                          ? `${colors.primary}15`
                          : 'transparent',
                      color:
                        mode.name === view.activeViewMode
                          ? colors.primary
                          : colors.mutedForeground,
                    }}
                    onMouseEnter={e => {
                      if (mode.name !== view.activeViewMode) {
                        e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                        e.currentTarget.style.color = colors.text;
                      }
                    }}
                    onMouseLeave={e => {
                      if (mode.name !== view.activeViewMode) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = colors.mutedForeground;
                      }
                    }}
                    aria-label={`Switch to ${mode.name} view`}
                    title={`${mode.name} view`}
                  >
                    {mode.icon}
                  </button>
                ))}
              </div>

              {/* Mobile Search Toggle */}
              <button
                className="lg:hidden p-2 rounded-md transition-colors duration-150"
                style={{
                  backgroundColor: 'transparent',
                  color: colors.mutedForeground,
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                  e.currentTarget.style.color = colors.text;
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = colors.mutedForeground;
                }}
                onClick={() => setIsSearchExpanded(!isSearchExpanded)}
                aria-label="Toggle search"
              >
                <SearchIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Search Overlay - Simplified */}
      {isSearchExpanded && (
        <div
          className="lg:hidden border-t"
          style={{ borderColor: colors.border }}
        >
          <div className="p-4">
            <form onSubmit={handleSearchSubmit} className="space-y-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div style={{ color: colors.mutedForeground }}>
                    <SearchIcon className="w-5 h-5" />
                  </div>
                </div>
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-3 w-full rounded-lg"
                  style={{
                    backgroundColor: colors.background,
                    border: 'none',
                    color: colors.text,
                  }}
                />
              </div>

              {/* Mobile Filter Pills - Simplified */}
              {view.search.filters && view.search.filters.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {view.search.filters.map(filter => (
                    <div
                      key={filter.id}
                      className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium"
                      style={{
                        backgroundColor: `${colors.primary}10`,
                        color: colors.text,
                      }}
                    >
                      <span>{filter.label}</span>
                      <button
                        onClick={() => view.search.onRemoveFilter?.(filter.id)}
                        className="p-0.5 rounded-full transition-colors duration-150"
                        style={{
                          backgroundColor: 'transparent',
                          color: colors.mutedForeground,
                        }}
                        onMouseEnter={e => {
                          e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                          e.currentTarget.style.color = colors.text;
                        }}
                        onMouseLeave={e => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                          e.currentTarget.style.color = colors.mutedForeground;
                        }}
                        aria-label={`Remove ${filter.label} filter`}
                      >
                        <XIcon className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </form>
          </div>
        </div>
      )}

      {/* Floating Action Button - Mobile - Simplified */}
      <div className="sm:hidden fixed bottom-6 right-6 z-50">
        {view.actions
          .filter(action => action.isPrimary)
          .map((action, index) => (
            <button
              key={index}
              onClick={action.onClick}
              className="w-12 h-12 rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-105"
              style={{
                backgroundColor: colors.primary,
                color: colors.primaryForeground,
              }}
              aria-label={action.label}
            >
              {action.label === 'New' && <PlusIcon className="w-5 h-5" />}
              {action.label === 'Upload' && <UploadIcon className="w-5 h-5" />}
            </button>
          ))}
      </div>
    </header>
  );
};

export default DynamicAppHeader;
